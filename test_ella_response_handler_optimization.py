#!/usr/bin/env python3
"""
测试Ella响应处理器优化后的功能
验证页面状态检查逻辑
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pages.apps.ella.ella_response_handler import <PERSON>ResponseHandler
from core.logger import log
from unittest.mock import Mock, MagicMock
import time


class MockStatusChecker:
    """模拟状态检查器"""
    
    def __init__(self, ella_process_status=True):
        self.ella_process_status = ella_process_status
    
    def ensure_ella_process(self):
        """模拟检查Ella进程"""
        return self.ella_process_status
    
    def set_ella_process_status(self, status):
        """设置Ella进程状态"""
        self.ella_process_status = status


class MockElement:
    """模拟UIAutomator2元素"""
    
    def __init__(self, exists=True, text="测试文本"):
        self._exists = exists
        self._text = text
    
    def exists(self):
        return self._exists
    
    def get_text(self):
        return self._text


class MockDriver:
    """模拟UIAutomator2驱动"""
    
    def __init__(self):
        self.elements = {}
        self.back_pressed = 0
        self.app_started = False
    
    def __call__(self, resourceId=None, **kwargs):
        # 根据resourceId返回不同的模拟元素
        if resourceId == "com.transsion.aivoiceassistant:id/asr_text":
            return self.elements.get("asr_text", MockElement(exists=True, text="打开蓝牙"))
        elif resourceId == "com.transsion.aivoiceassistant:id/robot_text":
            return self.elements.get("robot_text", MockElement(exists=True, text="蓝牙已打开"))
        elif resourceId == "com.transsion.aivoiceassistant:id/function_name":
            return self.elements.get("function_name", MockElement(exists=True, text="蓝牙"))
        elif resourceId == "com.transsion.aivoiceassistant:id/function_control":
            return self.elements.get("function_control", MockElement(exists=True, text="已打开"))
        else:
            return MockElement(exists=False, text="")
    
    def set_element(self, element_name, mock_element):
        """设置模拟元素"""
        self.elements[element_name] = mock_element
    
    def press(self, key):
        """模拟按键"""
        if key == "back":
            self.back_pressed += 1
    
    def app_start(self, package):
        """模拟启动应用"""
        self.app_started = True


def test_page_status_check():
    """测试页面状态检查功能"""
    print("🧪 测试页面状态检查功能...")
    
    # 创建模拟对象
    mock_driver = MockDriver()
    mock_status_checker = MockStatusChecker(ella_process_status=True)
    
    # 创建处理器
    handler = EllaResponseHandler(mock_driver, mock_status_checker)
    
    # 测试1: 在Ella页面时正常获取响应
    print("\n📋 测试1: 在Ella页面时正常获取响应")
    response = handler.get_response_text()
    print(f"响应文本: {response}")
    assert response == "蓝牙已打开", f"期望'蓝牙已打开'，实际得到'{response}'"
    print("✅ 测试1通过")
    
    # 测试2: 不在Ella页面时的处理
    print("\n📋 测试2: 不在Ella页面时的处理")
    mock_status_checker.set_ella_process_status(False)
    
    response = handler.get_response_text()
    print(f"不在Ella页面时的响应: '{response}'")
    print(f"返回键按压次数: {mock_driver.back_pressed}")
    print(f"应用启动状态: {mock_driver.app_started}")
    print("✅ 测试2通过")
    
    # 测试3: 没有状态检查器时的处理
    print("\n📋 测试3: 没有状态检查器时的处理")
    handler_no_checker = EllaResponseHandler(mock_driver, None)
    response = handler_no_checker.get_response_text()
    print(f"无状态检查器时的响应: {response}")
    assert response == "蓝牙已打开", f"期望'蓝牙已打开'，实际得到'{response}'"
    print("✅ 测试3通过")


def test_get_response_all_text():
    """测试获取所有响应文本功能"""
    print("\n🧪 测试获取所有响应文本功能...")
    
    # 创建模拟对象
    mock_driver = MockDriver()
    mock_status_checker = MockStatusChecker(ella_process_status=True)
    
    # 创建处理器
    handler = EllaResponseHandler(mock_driver, mock_status_checker)
    
    # 测试获取所有文本
    all_text = handler.get_response_all_text()
    print(f"所有响应文本: {all_text}")
    
    # 验证结果
    expected_texts = ["打开蓝牙", "蓝牙已打开", "蓝牙", "已打开"]
    assert all_text == expected_texts, f"期望{expected_texts}，实际得到{all_text}"
    print("✅ 获取所有响应文本测试通过")


def test_performance():
    """测试性能"""
    print("\n🚀 性能测试...")
    
    mock_driver = MockDriver()
    mock_status_checker = MockStatusChecker(ella_process_status=True)
    handler = EllaResponseHandler(mock_driver, mock_status_checker)
    
    # 测试多次调用的性能
    start_time = time.time()
    for i in range(50):
        handler.get_response_text()
        handler.get_response_all_text()
    
    end_time = time.time()
    print(f"50次调用耗时: {end_time - start_time:.3f}秒")
    print(f"平均每次调用耗时: {(end_time - start_time) / 100:.6f}秒")


if __name__ == "__main__":
    try:
        test_page_status_check()
        test_get_response_all_text()
        test_performance()
        print("\n🎉 所有测试通过!")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
