{"uid": "b1a8273437954620fa374b796ffaacdd", "name": "behaviors", "children": [{"name": "设备信息", "children": [{"name": "设备型号: TECNO CM8", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "7076375b905d4477", "parentUid": "909599d57996062ec07af67f58bf3885", "status": "passed", "time": {"start": 1753193150076, "stop": 1753193162899, "duration": 12823}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "909599d57996062ec07af67f58bf3885"}, {"name": "打开", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "7076375b905d4477", "parentUid": "e2a914691cac5198014efb72c568efb0", "status": "passed", "time": {"start": 1753193150076, "stop": 1753193162899, "duration": 12823}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e2a914691cac5198014efb72c568efb0"}], "uid": "9e40a99c1eef5f208d65955f7b1bb981"}, {"name": "Ella语音助手", "children": [{"name": "设备型号: TECNO CM8", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "7076375b905d4477", "parentUid": "08a882de4692d7cb6810138d7e4a0b02", "status": "passed", "time": {"start": 1753193150076, "stop": 1753193162899, "duration": 12823}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "08a882de4692d7cb6810138d7e4a0b02"}, {"name": "打开", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "7076375b905d4477", "parentUid": "4cfc2e89c37070d46c837256dd442628", "status": "passed", "time": {"start": 1753193150076, "stop": 1753193162899, "duration": 12823}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令 - 简洁版本", "uid": "80518fa216561f5c", "parentUid": "4cfc2e89c37070d46c837256dd442628", "status": "passed", "time": {"start": 1753193202596, "stop": 1753193211376, "duration": 8780}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令 - 简洁版本", "uid": "ba66cd4968010ed4", "parentUid": "4cfc2e89c37070d46c837256dd442628", "status": "passed", "time": {"start": 1753193236966, "stop": 1753193251816, "duration": 14850}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4cfc2e89c37070d46c837256dd442628"}, {"name": "联系人控制命令 - 简洁版本", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "347022a94d9f42a9", "parentUid": "5a62d9fbc0d3df56263439fe5374ad4d", "status": "passed", "time": {"start": 1753193168740, "stop": 1753193181972, "duration": 13232}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5a62d9fbc0d3df56263439fe5374ad4d"}, {"name": "联系人控制命令", "children": [{"name": "测试open contact命令", "uid": "3067993bcad541d8", "parentUid": "e4d92d6a5a89b66287c9ab32ec09d79a", "status": "passed", "time": {"start": 1753193187796, "stop": 1753193196884, "duration": 9088}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令", "uid": "b455797dbc4b5f49", "parentUid": "e4d92d6a5a89b66287c9ab32ec09d79a", "status": "passed", "time": {"start": 1753193217024, "stop": 1753193231002, "duration": 13978}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e4d92d6a5a89b66287c9ab32ec09d79a"}], "uid": "6f38a4272edded31ee153844d5e32f8b"}]}