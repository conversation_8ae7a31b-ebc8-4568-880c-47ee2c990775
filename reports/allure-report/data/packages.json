{"uid": "83edc06c07f9ae9e47eb6dd1b683e4e2", "name": "packages", "children": [{"name": "testcases.test_ella", "children": [{"name": "test_open_app", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "7076375b905d4477", "parentUid": "61d38b7285ebb9db4488fc6a5df6c4b5", "status": "passed", "time": {"start": 1753193150076, "stop": 1753193162899, "duration": 12823}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "61d38b7285ebb9db4488fc6a5df6c4b5"}, {"name": "test_open_bluetooth", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "347022a94d9f42a9", "parentUid": "7a126760506c054b2980b13f91df97c3", "status": "passed", "time": {"start": 1753193168740, "stop": 1753193181972, "duration": 13232}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7a126760506c054b2980b13f91df97c3"}, {"name": "test_open_contact", "children": [{"name": "测试open contact命令", "uid": "3067993bcad541d8", "parentUid": "a311c786cc44e5abec439f7b6d89b8b5", "status": "passed", "time": {"start": 1753193187796, "stop": 1753193196884, "duration": 9088}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a311c786cc44e5abec439f7b6d89b8b5"}, {"name": "test_open_ella", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "80518fa216561f5c", "parentUid": "bc0979e00b100b79f15faa03e3eb5a70", "status": "passed", "time": {"start": 1753193202596, "stop": 1753193211376, "duration": 8780}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bc0979e00b100b79f15faa03e3eb5a70"}, {"name": "test_open_phone", "children": [{"name": "测试open contact命令", "uid": "b455797dbc4b5f49", "parentUid": "4769bcbf682d0c7322435385fc0e3080", "status": "passed", "time": {"start": 1753193217024, "stop": 1753193231002, "duration": 13978}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4769bcbf682d0c7322435385fc0e3080"}, {"name": "test_open_wifi", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "ba66cd4968010ed4", "parentUid": "ea8223c5af5e308179769302bace21b2", "status": "passed", "time": {"start": 1753193236966, "stop": 1753193251816, "duration": 14850}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ea8223c5af5e308179769302bace21b2"}], "uid": "testcases.test_ella"}]}