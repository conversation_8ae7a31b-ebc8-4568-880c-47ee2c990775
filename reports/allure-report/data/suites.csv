"DESCRIPTION","DURATION IN MS","NAME","PARENT SUITE","START TIME","STATUS","STOP TIME","SUB SUITE","SUITE","TEST CLASS","TEST METHOD"
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","12823","测试open contact命令 - 简洁版本","testcases.test_ella","Tue Jul 22 22:05:50 CST 2025","passed","Tue Jul 22 22:06:02 CST 2025","TestEllaCommandConcise","test_open_app","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","9088","测试open contact命令","testcases.test_ella","Tue Jul 22 22:06:27 CST 2025","passed","Tue Jul 22 22:06:36 CST 2025","TestEllaContactCommandConcise","test_open_contact","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","14850","测试open contact命令 - 简洁版本","testcases.test_ella","Tue Jul 22 22:07:16 CST 2025","passed","Tue Jul 22 22:07:31 CST 2025","TestEllaCommandConcise","test_open_wifi","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","13232","测试open contact命令 - 简洁版本","testcases.test_ella","Tue Jul 22 22:06:08 CST 2025","passed","Tue Jul 22 22:06:21 CST 2025","TestEllaCommandConcise","test_open_bluetooth","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","13978","测试open contact命令","testcases.test_ella","Tue Jul 22 22:06:57 CST 2025","passed","Tue Jul 22 22:07:11 CST 2025","TestEllaContactCommandConcise","test_open_phone","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","8780","测试open contact命令 - 简洁版本","testcases.test_ella","Tue Jul 22 22:06:42 CST 2025","passed","Tue Jul 22 22:06:51 CST 2025","TestEllaCommandConcise","test_open_ella","",""
