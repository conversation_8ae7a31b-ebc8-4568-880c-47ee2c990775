{"uid": "98d3104e051c652961429bf95fa0b5d6", "name": "suites", "children": [{"name": "testcases.test_ella", "children": [{"name": "test_open_app", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "7076375b905d4477", "parentUid": "722879c41215532d36edadc348653736", "status": "passed", "time": {"start": 1753193150076, "stop": 1753193162899, "duration": 12823}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "722879c41215532d36edadc348653736"}], "uid": "3aa51a5c5e730741ba37053a102abba0"}, {"name": "test_open_bluetooth", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "347022a94d9f42a9", "parentUid": "f272056d0d6e7777e65d2891da10e279", "status": "passed", "time": {"start": 1753193168740, "stop": 1753193181972, "duration": 13232}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f272056d0d6e7777e65d2891da10e279"}], "uid": "2327e1268017d9723bb024e189ec7a3a"}, {"name": "test_open_contact", "children": [{"name": "TestEllaContactCommandConcise", "children": [{"name": "测试open contact命令", "uid": "3067993bcad541d8", "parentUid": "e50a17fd82052f717a7ff82e0822a7cf", "status": "passed", "time": {"start": 1753193187796, "stop": 1753193196884, "duration": 9088}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e50a17fd82052f717a7ff82e0822a7cf"}], "uid": "7e9470b3933c155960adfdca523f0f5f"}, {"name": "test_open_ella", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "80518fa216561f5c", "parentUid": "01b45279ecd0bde4691d3d6b88426772", "status": "passed", "time": {"start": 1753193202596, "stop": 1753193211376, "duration": 8780}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "01b45279ecd0bde4691d3d6b88426772"}], "uid": "58eb3538081307aaf6dbff85d1fdb359"}, {"name": "test_open_phone", "children": [{"name": "TestEllaContactCommandConcise", "children": [{"name": "测试open contact命令", "uid": "b455797dbc4b5f49", "parentUid": "e227a44553a3c391a30b3d7be7dede21", "status": "passed", "time": {"start": 1753193217024, "stop": 1753193231002, "duration": 13978}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e227a44553a3c391a30b3d7be7dede21"}], "uid": "6b9725377ee79b29f0ae60b0702464cc"}, {"name": "test_open_wifi", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "ba66cd4968010ed4", "parentUid": "d2f5f9678e5430ded01ffae9df17ce32", "status": "passed", "time": {"start": 1753193236966, "stop": 1753193251816, "duration": 14850}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d2f5f9678e5430ded01ffae9df17ce32"}], "uid": "be5b73443cecbb76017b23e6e3099f1b"}], "uid": "94b4d2a35e7a0e6ad70c14ed2152a386"}]}