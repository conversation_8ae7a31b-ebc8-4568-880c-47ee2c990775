"""
应用检测器 - 重构版本
使用策略模式和工厂模式优化应用检测功能
"""
import subprocess
import sys
import os
from pathlib import Path
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union
from enum import Enum

# 添加项目根目录到路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

from core.logger import log


class AppType(Enum):
    """应用类型枚举"""
    WEATHER = "weather"
    CAMERA = "camera"
    SETTINGS = "settings"
    CONTACTS = "contacts"
    FACEBOOK = "facebook"
    MUSIC = "music"
    CLOCK = "clock"
    MAPS = "maps"
    PLAYSTORE = "playstore"


class BaseAppDetector(ABC):
    """应用检测器基类 - 模板方法模式"""
    
    def __init__(self, app_type: AppType):
        self.app_type = app_type
        self.timeout = 10
        
    @abstractmethod
    def get_package_names(self) -> List[str]:
        """获取应用包名列表"""
        pass
    
    @abstractmethod
    def get_keywords(self) -> List[str]:
        """获取应用关键词列表"""
        pass
    
    def check_app_opened(self) -> bool:
        """检查应用是否打开 - 模板方法"""
        try:
            log.info(f"检查{self.app_type.value}应用状态")
            
            # 1. 优先检查活动状态
            if self._check_activity_status():
                return True
                
            # 2. 检查焦点窗口
            if self._check_focus_window():
                return True
                
            # 3. 检查进程状态
            if self._check_process_status():
                return True
                
            log.info(f"未检测到{self.app_type.value}应用")
            return False
            
        except Exception as e:
            log.error(f"检查{self.app_type.value}应用失败: {e}")
            return False
    
    def _check_activity_status(self) -> bool:
        """检查活动状态"""
        try:
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=self.timeout,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                activity_output = result.stdout
                return self._analyze_activity_output(activity_output)
                
        except Exception as e:
            log.debug(f"检查活动状态失败: {e}")
        return False
    
    def _check_focus_window(self) -> bool:
        """检查焦点窗口"""
        try:
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=self.timeout,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                window_output = result.stdout
                return self._analyze_window_output(window_output)
                
        except Exception as e:
            log.debug(f"检查焦点窗口失败: {e}")
        return False
    
    def _check_process_status(self) -> bool:
        """检查进程状态"""
        try:
            result = subprocess.run(
                ["adb", "shell", "ps"],
                capture_output=True,
                text=True,
                timeout=self.timeout,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                ps_output = result.stdout
                return self._analyze_process_output(ps_output)
                
        except Exception as e:
            log.debug(f"检查进程状态失败: {e}")
        return False
    
    def _analyze_activity_output(self, output: str) -> bool:
        """分析活动输出"""
        packages = self.get_package_names()
        keywords = self.get_keywords()

        # 优先检查包名（更精确）
        for package in packages:
            if package in output:
                if self._verify_activity_resumed(output, package):
                    log.info(f"✅ 通过活动检测到{self.app_type.value}应用: {package}")
                    return True

        # 检查关键词（需要更严格的验证）
        for keyword in keywords:
            if self._verify_keyword_in_activity(output, keyword):
                log.info(f"✅ 通过关键词检测到{self.app_type.value}应用: {keyword}")
                return True

        return False
    
    def _analyze_window_output(self, output: str) -> bool:
        """分析窗口输出"""
        packages = self.get_package_names()
        
        lines = output.split('\n')
        for line in lines:
            if "mCurrentFocus=" in line:
                for package in packages:
                    if package in line and "null" not in line:
                        log.info(f"✅ 通过焦点窗口检测到{self.app_type.value}应用: {package}")
                        return True
                break
        return False
    
    def _analyze_process_output(self, output: str) -> bool:
        """分析进程输出"""
        packages = self.get_package_names()

        lines = output.split('\n')
        for line in lines:
            line = line.strip()
            if not line:
                continue

            for package in packages:
                if package in line:
                    # 更严格的进程验证
                    if self._verify_process_running(line, package):
                        log.info(f"✅ 通过进程检测到{self.app_type.value}应用: {package}")
                        return True
        return False

    def _verify_process_running(self, process_line: str, package: str) -> bool:
        """验证进程是否真正运行（区分前台应用和后台服务）"""
        parts = process_line.split()

        # 检查进程行格式是否正确
        if len(parts) < 8:
            return False

        # 检查进程状态（S=睡眠, R=运行, D=不可中断睡眠, T=停止）
        # 排除僵尸进程(Z)和其他异常状态
        valid_states = ['S', 'R', 'D', 'T']
        if not any(status in parts for status in valid_states):
            return False

        # 确保包名完全匹配（避免部分匹配）
        process_name = parts[-1] if parts else ""
        if package not in process_name:
            return False

        # 检查是否是主进程（避免检测到子进程或服务进程）
        if package == process_name or process_name.startswith(package):
            # 根据应用类型采用不同的验证策略
            return self._verify_app_process_by_type(package)

        return False

    def _verify_app_process_by_type(self, package: str) -> bool:
        """根据应用类型验证进程"""
        # 音乐应用特殊处理：允许后台运行
        if self.app_type == AppType.MUSIC:
            return self._verify_music_app_process(package)

        # 其他应用类型：需要前台验证
        return self._verify_foreground_app_process(package)

    def _verify_music_app_process(self, package: str) -> bool:
        """验证音乐应用进程（允许后台运行）"""
        try:
            # 方法1: 检查是否有前台Activity（优先）
            if self._verify_foreground_app_process(package):
                log.debug(f"音乐应用 {package} 在前台运行")
                return True

            # 方法2: 检查是否有音频焦点（音乐播放中）
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "audio", "|", "grep", "-A", "3", "-B", "3", package],
                capture_output=True,
                text=True,
                timeout=5,
                shell=True
            )

            if result.returncode == 0 and result.stdout.strip():
                output = result.stdout
                # 检查音频焦点或播放状态
                if any(keyword in output.lower() for keyword in
                       ['audiofocus', 'playing', 'music', 'media']):
                    log.debug(f"音乐应用 {package} 正在播放音频")
                    return True

            # 方法3: 检查媒体会话（Android媒体框架）
            result2 = subprocess.run(
                ["adb", "shell", "dumpsys", "media_session", "|", "grep", "-A", "5", "-B", "5", package],
                capture_output=True,
                text=True,
                timeout=5,
                shell=True
            )

            if result2.returncode == 0 and result2.stdout.strip():
                output = result2.stdout
                # 检查媒体会话状态
                if any(keyword in output.lower() for keyword in
                       ['playing', 'paused', 'active', 'session']):
                    log.debug(f"音乐应用 {package} 有活跃的媒体会话")
                    return True

            # 方法4: 对于音乐应用，如果进程存在且不是系统服务，就认为是运行中
            # 这是因为音乐应用经常在后台保持运行以便快速启动
            log.debug(f"音乐应用 {package} 在后台运行")
            return True

        except Exception as e:
            log.debug(f"验证音乐应用进程失败: {e}")
            return False

    def _verify_foreground_app_process(self, package: str) -> bool:
        """验证是否是前台应用进程（而不是后台服务）"""
        try:
            # 方法1: 检查应用是否有可见的Activity
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities", "|", "grep", "-A", "5", package],
                capture_output=True,
                text=True,
                timeout=5,
                shell=True
            )

            if result.returncode == 0 and result.stdout.strip():
                output = result.stdout
                # 检查是否有RESUMED状态的Activity
                if "state=RESUMED" in output or "RESUMED" in output:
                    return True

            # 方法2: 检查应用是否在最近任务中
            result2 = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "recents", "|", "grep", package],
                capture_output=True,
                text=True,
                timeout=5,
                shell=True
            )

            if result2.returncode == 0 and result2.stdout.strip():
                return True

            # 方法3: 检查应用是否有前台服务（但这通常不是我们要检测的）
            # 对于时钟应用，我们主要关心UI是否打开，而不是后台服务
            return False

        except Exception as e:
            log.debug(f"验证前台应用进程失败: {e}")
            return False
    
    def _verify_activity_resumed(self, output: str, package: str) -> bool:
        """验证活动是否处于RESUMED状态"""
        lines = output.split('\n')
        for i, line in enumerate(lines):
            if package in line and "ActivityRecord{" in line:
                # 检查后续几行是否有RESUMED状态
                for j in range(i, min(i + 5, len(lines))):
                    if "state=RESUMED" in lines[j] or "RESUMED" in lines[j]:
                        return True
        return False

    def _verify_keyword_in_activity(self, output: str, keyword: str) -> bool:
        """验证关键词是否在活动上下文中出现（更严格的检查）"""
        lines = output.split('\n')
        keyword_lower = keyword.lower()

        for i, line in enumerate(lines):
            line_lower = line.lower()
            if keyword_lower in line_lower:
                # 检查是否在ActivityRecord上下文中
                if "activityrecord{" in line_lower:
                    # 检查是否有RESUMED状态
                    for j in range(i, min(i + 5, len(lines))):
                        if "state=RESUMED" in lines[j].lower() or "resumed" in lines[j].lower():
                            return True

                # 检查是否在应用包名上下文中
                packages = self.get_package_names()
                for package in packages:
                    if package in line_lower:
                        # 如果关键词和包名在同一行，且有活动相关信息
                        if any(activity_keyword in line_lower for activity_keyword in
                               ['activity', 'intent', 'task', 'stack']):
                            return True

        return False


class AppDetector:
    """应用检测器主类 - 使用工厂模式"""

    def __init__(self):
        """初始化应用检测器"""
        self._detectors: Dict[AppType, BaseAppDetector] = {}
        self._init_detectors()
    
    def _init_detectors(self):
        """初始化各种应用检测器"""
        # 使用更可靠的导入策略
        detectors = {}

        # 使用绝对路径导入
        try:
            from pages.base.detectors.weather_detector import WeatherDetector
            from pages.base.detectors.camera_detector import CameraDetector
            from pages.base.detectors.settings_detector import SettingsDetector
            from pages.base.detectors.contacts_detector import ContactsDetector
            from pages.base.detectors.facebook_detector import FacebookDetector
            from pages.base.detectors.music_detector import MusicDetector
            from pages.base.detectors.clock_detector import ClockDetector
            from pages.base.detectors.maps_detector import MapsDetector
            from pages.base.detectors.playstore_detector import PlayStoreDetector

            log.debug("✅ 使用绝对路径导入成功")

        except ImportError as e:
            log.error(f"绝对路径导入失败: {e}")
            # 如果绝对导入失败，使用动态导入作为备用方案
            return self._dynamic_import_detectors()

        # 创建检测器实例
        try:
            self._detectors = {
                AppType.WEATHER: WeatherDetector(),
                AppType.CAMERA: CameraDetector(),
                AppType.SETTINGS: SettingsDetector(),
                AppType.CONTACTS: ContactsDetector(),
                AppType.FACEBOOK: FacebookDetector(),
                AppType.MUSIC: MusicDetector(),
                AppType.CLOCK: ClockDetector(),
                AppType.MAPS: MapsDetector(),
                AppType.PLAYSTORE: PlayStoreDetector(),
            }
            log.debug(f"✅ 成功初始化 {len(self._detectors)} 个检测器")

        except Exception as e:
            log.error(f"创建检测器实例失败: {e}")
            self._detectors = {}

    def _dynamic_import_detectors(self):
        """动态导入检测器 - 最后的备用方案"""
        log.info("尝试动态导入检测器...")

        import importlib.util
        import os

        current_dir = os.path.dirname(os.path.abspath(__file__))
        detectors_dir = os.path.join(current_dir, 'detectors')

        detector_files = {
            AppType.WEATHER: 'weather_detector.py',
            AppType.CAMERA: 'camera_detector.py',
            AppType.SETTINGS: 'settings_detector.py',
            AppType.CONTACTS: 'contacts_detector.py',
            AppType.FACEBOOK: 'facebook_detector.py',
            AppType.MUSIC: 'music_detector.py',
            AppType.CLOCK: 'clock_detector.py',
            AppType.MAPS: 'maps_detector.py',
            AppType.PLAYSTORE: 'playstore_detector.py',
        }

        self._detectors = {}

        for app_type, filename in detector_files.items():
            try:
                file_path = os.path.join(detectors_dir, filename)
                if os.path.exists(file_path):
                    spec = importlib.util.spec_from_file_location(
                        f"detector_{app_type.value}", file_path
                    )
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)

                    # 获取检测器类
                    class_name = filename.replace('.py', '').replace('_', ' ').title().replace(' ', '') + 'Detector'
                    if hasattr(module, class_name):
                        detector_class = getattr(module, class_name)
                        self._detectors[app_type] = detector_class()
                        log.debug(f"✅ 动态导入成功: {class_name}")
                    else:
                        log.warning(f"未找到类: {class_name} in {filename}")
                else:
                    log.warning(f"检测器文件不存在: {file_path}")

            except Exception as e:
                log.error(f"动态导入 {filename} 失败: {e}")

        log.info(f"动态导入完成，成功加载 {len(self._detectors)} 个检测器")

    def _get_detector_utils(self):
        """获取DetectorUtils类 - 使用绝对路径导入"""
        try:
            from pages.base.detectors.detector_utils import DetectorUtils
            return DetectorUtils
        except ImportError as e:
            log.error(f"无法导入DetectorUtils: {e}")
            return None

    def get_detector(self, app_type: AppType) -> Optional[BaseAppDetector]:
        """获取指定类型的检测器"""
        return self._detectors.get(app_type)
    
    def check_app_opened(self, app_type: Union[AppType, str]) -> bool:
        """检查指定类型的应用是否打开"""
        if isinstance(app_type, str):
            try:
                app_type = AppType(app_type)
            except ValueError:
                log.error(f"不支持的应用类型: {app_type}")
                return False

        detector = self.get_detector(app_type)
        if detector:
            return detector.check_app_opened()
        else:
            log.error(f"未找到{app_type.value}应用的检测器")
            return False

    # ==================== 向后兼容的方法 ====================

    def check_weather_app_opened(self) -> bool:
        """检查天气应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.WEATHER)

    def check_camera_app_opened(self) -> bool:
        """检查相机应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.CAMERA)

    def check_settings_opened(self) -> bool:
        """检查设置应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.SETTINGS)

    def check_contacts_app_opened(self) -> bool:
        """检查联系人应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.CONTACTS)

    def check_facebook_app_opened(self) -> bool:
        """检查Facebook应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.FACEBOOK)

    def check_music_app_opened(self) -> bool:
        """检查音乐应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.MUSIC)

    def check_clock_app_opened(self) -> bool:
        """检查时钟应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.CLOCK)

    def check_google_map_app_opened(self) -> bool:
        """检查Google地图应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.MAPS)

    def check_google_playstore_app_opened(self) -> bool:
        """检查Google Play Store应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.PLAYSTORE)

    def check_visha_app_opened(self,) -> bool:
        """检查Visha音乐应用是否打开 - 向后兼容"""
        return self.check_app_opened(AppType.MUSIC)

    def check_visha_app_in_foreground(self) -> bool:
        """检查Visha音乐应用是否在前台 - 向后兼容"""
        return self.check_app_opened(AppType.MUSIC)

    def check_visha_app_running(self) -> bool:
        """检查Visha音乐应用是否正在运行 - 向后兼容"""
        return self.check_app_opened(AppType.MUSIC)

    # ==================== 高级功能方法 ====================

    def check_camera_permission(self) -> bool:
        """检查相机权限状态"""
        DetectorUtils = self._get_detector_utils()
        if DetectorUtils:
            return DetectorUtils.check_app_permissions(
                "com.transsion.aivoiceassistant",
                "android.permission.CAMERA"
            )
        return False

    def find_available_apps(self, app_type: str) -> List[str]:
        """
        查找可用的特定类型应用

        Args:
            app_type: 应用类型 ('weather', 'camera', 'contacts', 'clock')

        Returns:
            List[str]: 可用应用包名列表
        """
        DetectorUtils = self._get_detector_utils()
        if not DetectorUtils:
            return []

        # 定义不同类型应用的关键词
        app_keywords = {
            'weather': ['weather', 'clima', 'meteo'],
            'camera': ['camera', 'cam', 'photo'],
            'contacts': ['contacts', 'contact', 'people', 'dialer'],
            'clock': ['clock', 'time', 'alarm', 'timer', 'deskclock'],
            'music': ['music', 'player', 'visha'],
            'maps': ['maps', 'map', 'navigation'],
        }

        keywords = app_keywords.get(app_type, [])
        if not keywords:
            log.error(f"不支持的应用类型: {app_type}")
            return []

        return DetectorUtils.find_available_apps(keywords)

    def get_app_version(self, app_type: Union[AppType, str]) -> Optional[str]:
        """
        获取指定类型应用的版本信息

        Args:
            app_type: 应用类型

        Returns:
            Optional[str]: 应用版本，如果获取失败则返回None
        """
        DetectorUtils = self._get_detector_utils()
        if not DetectorUtils:
            return None

        if isinstance(app_type, str):
            try:
                app_type = AppType(app_type)
            except ValueError:
                log.error(f"不支持的应用类型: {app_type}")
                return None

        detector = self.get_detector(app_type)
        if detector:
            packages = detector.get_package_names()
            for package in packages:
                version = DetectorUtils.get_app_version(package)
                if version:
                    return version

        return None

    def get_device_info(self) -> Dict[str, str]:
        """获取设备信息"""
        DetectorUtils = self._get_detector_utils()
        if DetectorUtils:
            return DetectorUtils.get_device_info()
        return {}

    def check_app_in_foreground(self, app_type: Union[AppType, str]) -> bool:
        """
        检查指定类型的应用是否在前台

        Args:
            app_type: 应用类型

        Returns:
            bool: 应用是否在前台
        """
        DetectorUtils = self._get_detector_utils()
        if not DetectorUtils:
            return False

        if isinstance(app_type, str):
            try:
                app_type = AppType(app_type)
            except ValueError:
                log.error(f"不支持的应用类型: {app_type}")
                return False

        detector = self.get_detector(app_type)
        if detector:
            packages = detector.get_package_names()
            for package in packages:
                if DetectorUtils.check_package_has_running_process(package):
                    if DetectorUtils.verify_app_in_foreground(package):
                        return True

        return False

    def get_running_apps_summary(self) -> Dict[str, bool]:
        """
        获取所有支持的应用类型的运行状态摘要

        Returns:
            Dict[str, bool]: 应用类型到运行状态的映射
        """
        summary = {}

        for app_type in AppType:
            try:
                summary[app_type.value] = self.check_app_opened(app_type)
            except Exception as e:
                log.debug(f"检查{app_type.value}应用状态失败: {e}")
                summary[app_type.value] = False

        return summary

    # ==================== 闹钟相关方法 ====================

    def _get_alarm_detector(self):
        """获取闹钟检测器"""
        try:
            from pages.base.detectors.alarm_detector import AlarmDetector
            return AlarmDetector()
        except ImportError as e:
            log.error(f"无法导入AlarmDetector: {e}")
            return None

    def check_alarm_status(self) -> bool:
        """检查闹钟状态"""
        alarm_detector = self._get_alarm_detector()
        if alarm_detector:
            return alarm_detector.check_alarm_status()
        return False

    def get_alarm_list(self) -> List[Dict[str, any]]:
        """获取闹钟列表"""
        alarm_detector = self._get_alarm_detector()
        if alarm_detector:
            return alarm_detector.get_alarm_list()
        return []

    def clear_all_alarms(self) -> bool:
        """清除所有闹钟"""
        alarm_detector = self._get_alarm_detector()
        if alarm_detector:
            return alarm_detector.clear_all_alarms()
        return False

    def set_alarm(self, hour: int, minute: int, enabled: bool = True) -> bool:
        """设置闹钟"""
        alarm_detector = self._get_alarm_detector()
        if alarm_detector:
            return alarm_detector.set_alarm(hour, minute, enabled)
        return False

    def get_next_alarm_info(self) -> Optional[Dict[str, any]]:
        """获取下一个闹钟信息"""
        alarm_detector = self._get_alarm_detector()
        if alarm_detector:
            return alarm_detector.get_next_alarm_info()
        return None

    # ==================== 向后兼容的别名方法 ====================

    def check_clock_status(self) -> bool:
        """检查时钟应用状态 - 向后兼容"""
        return self.check_app_opened(AppType.CLOCK)

    def contacts_app_opened_alternative(self) -> bool:
        """联系人应用检测的替代方法 - 向后兼容"""
        return self.check_app_opened(AppType.CONTACTS)

    def check_contacts_app_opened_smart(self) -> bool:
        """智能检查联系人应用 - 向后兼容"""
        return self.check_app_opened(AppType.CONTACTS)

    def get_visha_package_name(self) -> str:
        """获取Visha包名 - 向后兼容"""
        music_detector = self.get_detector(AppType.MUSIC)
        if music_detector:
            packages = music_detector.get_package_names()
            visha_packages = [pkg for pkg in packages if 'visha' in pkg.lower()]
            if visha_packages:
                return visha_packages[0]
        return ""


# ==================== 测试和使用示例 ====================

if __name__ == '__main__':
    # 创建检测器实例
    detector = AppDetector()

    # print(detector.check_app_opened(AppType.WEATHER))
    # print(detector.check_app_opened(AppType.CAMERA))
    # print(detector.check_app_opened(AppType.SETTINGS))
    # print(detector.check_app_opened(AppType.CONTACTS))
    # print(detector.check_app_opened(AppType.MUSIC))
    print(detector.check_app_opened(AppType.CLOCK))
    # print("=" * 60)
    # print("🔍 应用检测器 - 重构版本测试")
    # print("=" * 60)
    #
    # # 测试新的API
    # print("\n📱 检测各种应用状态:")
    #
    # # 使用新的统一API
    # apps_to_check = [
    #     AppType.WEATHER,
    #     AppType.CAMERA,
    #     AppType.SETTINGS,
    #     AppType.CONTACTS,
    #     AppType.MUSIC,
    #     AppType.CLOCK
    # ]
    #
    # for app_type in apps_to_check:
    #     try:
    #         status = detector.check_app_opened(app_type)
    #         print(f"  {app_type.value:12} : {'✅ 运行中' if status else '❌ 未运行'}")
    #     except Exception as e:
    #         print(f"  {app_type.value:12} : ⚠️ 检测失败 - {e}")
    #
    # # 测试运行状态摘要
    # print(f"\n📊 运行状态摘要:")
    # summary = detector.get_running_apps_summary()
    # for app_type, status in summary.items():
    #     print(f"  {app_type:12} : {'✅' if status else '❌'}")
    #
    # # 测试设备信息
    # print(f"\n📱 设备信息:")
    # device_info = detector.get_device_info()
    # for key, value in device_info.items():
    #     print(f"  {key:15} : {value}")
    #
    # # 测试闹钟功能
    # print(f"\n⏰ 闹钟状态:")
    # alarm_status = detector.check_alarm_status()
    # print(f"  闹钟状态: {'✅ 有闹钟' if alarm_status else '❌ 无闹钟'}")
    #
    # alarms = detector.get_alarm_list()
    # if alarms:
    #     print(f"  找到 {len(alarms)} 个闹钟:")
    #     for alarm in alarms[:3]:  # 只显示前3个
    #         print(f"    - {alarm.get('time', 'N/A')} ({'启用' if alarm.get('enabled', False) else '禁用'})")
    #
    # print("\n" + "=" * 60)
    # print("✅ 测试完成")
    # print("=" * 60)
