"""
Ella语音助手第三方集成指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("第三方集成")
class TestEllaHowIsWheatherToday(SimpleEllaTest):
    """Ella how is the wheather today 测试类"""
    command = "how is the wheather today"
    # 使用关键词模式匹配，验证天气响应的核心要素
    # 不再硬编码具体的地名、天气状况和温度数值
    expected_text = [
        "today",  # 确认是今天的天气
        "°C",     # 确认包含温度信息
        "high",   # 确认包含最高温度
        "low"     # 确认包含最低温度
    ]

    @allure.title(f"测试{command}能正常执行")
    @allure.description(f"{command}")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_how_is_the_wheather_today(self, ella_app):
        f"""{self.command}"""

        command = self.command

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text = self.simple_command_test(
                ella_app, command, verify_status=False  # 第三方集成通常不验证状态
            )

        with allure.step("验证天气查询响应"):
            # 使用智能天气验证方法
            result = self.verify_weather_response(response_text)
            assert result, f"天气响应验证失败，实际响应: '{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
