"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手") # 功能模块（最高层级）
@allure.story("打开") # 用户故事（中层级）
class TestEllaCommandConcise(SimpleEllaTest):
    """Ella联系人命令测试类"""

    @allure.title("测试open contact命令 - 简洁版本") # 测试标题（具体测试）
    @allure.description("使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier") # 测试描述
    @allure.severity(allure.severity_level.CRITICAL) # 测试级别
    @pytest.mark.smoke # 冒烟测试
    def test_open_app(self, ella_app):
        """测试open contact命令 - 简洁版本"""
        command = "open app" # query

        app_name = 'app'

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含Done"):
            # expected_text =["YouTube","Instagram","Visha","which app should i open"]
            expected_text =["which app should i open"]
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含'Done'，实际响应: '{response_text}'"


        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            # 添加额外的验证信息
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")

        # pytest测试函数不应该返回值，所有验证都应该通过assert完成
